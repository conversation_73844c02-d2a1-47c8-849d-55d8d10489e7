from pyVmomi import vim
from pyVim.connect import SmartConnect, Disconnect
import ssl
from host import hosts, ssh_execute_command, sendDing  # 引入主机信息

def revert_to_snapshot(vm, snapshot_name):
    """
    将虚拟机还原到指定的快照点并重新打开设备电源
    """
    snapshot = find_snapshot_by_name(vm.snapshot.rootSnapshotList, snapshot_name)
    if snapshot:
        print("找到快照:", snapshot_name)
        task = snapshot.snapshot.RevertToSnapshot_Task()
        task_result = wait_for_task(task)
        if task_result == vim.TaskInfo.State.success:
            print("虚拟机成功还原到快照:", snapshot_name)
            power_on_vm(vm)
        else:
            print("虚拟机还原到快照时出现错误")
    else:
        print("未找到快照:", snapshot_name)

def find_snapshot_by_name(snapshots, snapshot_name):
    """
    根据快照名称在快照链中查找指定的快照
    """
    for snapshot in snapshots:
        if snapshot.name == snapshot_name:
            return snapshot
        child_snapshot = find_snapshot_by_name(snapshot.childSnapshotList, snapshot_name)
        if child_snapshot:
            return child_snapshot
    return None

def wait_for_task(task):
    """
    等待任务完成
    """
    while task.info.state == vim.TaskInfo.State.running:
        pass
    return task.info.state

def power_on_vm(vm):
    """
    打开虚拟机电源
    """
    if vm.runtime.powerState != vim.VirtualMachine.PowerState.poweredOn:
        print("虚拟机电源已关闭，正在重新打开电源...")
        task = vm.PowerOnVM_Task()
        wait_for_task(task)
        print("虚拟机电源已重新打开")
    else:
        print("虚拟机电源已经是打开状态")

def get_vm_snapshots(vm):
    """
    获取虚拟机的快照信息
    """
    print("虚拟机名称:", vm.name)
    snapshots = vm.snapshot.rootSnapshotList
    if snapshots:
        print("快照数量:", len(snapshots))
        for snapshot in snapshots:
            print_snapshot_info(snapshot)

def print_snapshot_info(snapshot, indent=0):
    """
    递归打印快照信息
    """
    print("  " * indent + "快照名称:", snapshot.name)
    print("  " * indent + "创建时间:", snapshot.createTime)
    print("  " * indent + "描述:", snapshot.description)
    print("  " * indent + "===")
    
    # 递归打印子快照信息
    if snapshot.childSnapshotList:
        for child_snapshot in snapshot.childSnapshotList:
            print_snapshot_info(child_snapshot, indent+1)

def task_main(vcenter_ip, username, password, hostname, snapshot_name, ssl_context):
    # 连接 vCenter Server
    si = SmartConnect(host=vcenter_ip, user=username, pwd=password, sslContext=ssl_context)
    if not si:
        print("无法连接到 vCenter Server")
        return

    # 获取数据中心对象
    content = si.RetrieveContent()
    datacenter = content.rootFolder.childEntity[0]

    # 获取所有虚拟机
    vm = None
    for entity in datacenter.vmFolder.childEntity:
        if isinstance(entity, vim.VirtualMachine) and entity.name.find(hostname) > -1:
            print('======' + entity.name +'======')
            revert_to_snapshot(entity, snapshot_name)
            print('======end======')

    # 断开与 vCenter Server 的连接
    Disconnect(si)

if __name__ == "__main__":
    # 忽略 SSL 证书验证
    ssl_context = ssl.SSLContext(ssl.PROTOCOL_TLS_CLIENT)
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE
    # vSphere 连接信息

    # task_main(
    #     "*************", "root", "Infogo12#", "LZG_172.22.108.100", "3746.LTS02.SP4", ssl_context
    # )
    # task_main(
    #     "*************", "root", "Infogo12#", "LZG_172.22.108.101", "3746.LTS02.SP4", ssl_context
    # )
    # task_main(
    #     "*************", "root", "Infogo12#", "LZG_**************", "3746.LTS02.SP4", ssl_context
    # )
    messages = []
    configs = [
    #  ["*************", "root", "Infogo12#", "LZG_**************", "3746.LTS02.SP4", ssl_context, "**************"],
    #   ["*************", "root", "Infogo12#", "LZG_**************", "3746.LTS02.SP4", ssl_context, "**************"],
    #   ["***********", "root", "infogo@123", "duanyc_*************", "ZTPR01S02", ssl_context, "*************"],
    #    ["***********", "root", "infogo@123", "duanyc_*************", "3746.R004.SP5", ssl_context, "*************"],
    #    ["***********", "root", "infogo@123", "duanyc_172.20.29.235", "3746.R004.SP5", ssl_context, "172.20.29.235"],
        ["***********", "duanyc", "EfwY852Pht58e#", "duanyc_172.20.29.233", "Ubuntu 22", ssl_context, "172.20.29.233"],
    #    ["172.24.43.237", "root", "ifztp2022@asd", "duanyc_172.24.43.237_ubuntu_asec", "Ubuntu 22", ssl_context, "172.24.43.2"],
    #    ["172.31.92.254", "root", "infogoiot_123", "duanyc_172.31.92.232_ubuntu24_bt", "Ubuntu", ssl_context, "172.31.92.232"],
    #    ["***********", "root", "infogo@123", "duanyc_172.20.29.234", "3746.R004.SP4", ssl_context, "172.20.29.234"]
    ]

    for configOne in configs:
        task_main(
            configOne[0], configOne[1], configOne[2], configOne[3], configOne[4], configOne[5]
        )
        messages.append(f"自动恢复{configOne[6]}虚拟机快照至{configOne[4]}版本！")
    
    print("\n".join(messages))
    token = "7f2829a52f1016a6a5fa01f837ade7d9fa9daf1fee2ccdfec118cf4568c5c211"
    allMessage = "\n".join(messages)
    sendDing(f"\n{allMessage}", token)

    # 忽略 SSL 证书验证
    # ssl_context2 = ssl.SSLContext(ssl.PROTOCOL_TLSv1)
    # ssl_context2.set_ciphers("DEFAULT:@SECLEVEL=1")
    # ssl_context2.check_hostname = False
    # ssl_context2.verify_mode = ssl.CERT_NONE
    # task_main('*************', 'root', 'infogo!@#', "************", "3746.R002", ssl_context2)
