#!/bin/bash
set -e
# 记录所有参数（原样保留引号和空格）
echo "[$(date)] $0 $@" >> last.log
generate_config() {
    local os_type=$1
    local version=$2
    local filename=$3
	# 文件在上一层目录
	filename=../"$filename"

    # 从文件名中提取版本号和构建信息
    #version_info=$(echo "$filename" | sed -E 's/.*_([0-9]+\.[0-9]+\.[0-9]+\.[0-9]+)\s*Build([0-9]+).*/\1-dev Build\2/')
    #echo "提取的版本信息: $version_info"
	# 使用传入的version而不是文件名提取的
	version_info="$version"

    # 计算文件 MD5 和 SHA256 值
    echo "计算 MD5 和 SHA256 值..."
    md5_value=$(md5sum "$filename" | awk '{print $1}')
    sha256_value=$(sha256sum "$filename" | awk '{print $1}')
    echo "MD5: $md5_value"
    echo "SHA256: $sha256_value"

    # 目录路径
    output_dir="agents/$os_type/$version_info"

    # 创建新目录
    echo "创建新目录: $output_dir"
    mkdir -p "$output_dir"

    # 修改 config.json 文件
    echo "修改 config.json 文件..."
    touch "agents/$os_type/config.json"
    cat <<EOF > "agents/$os_type/config.json"
[
    {
        "version": "$version_info",
        "next_version": "",
        "diff_upgrade": 0,
        "note": [
            "1.已知缺陷修复"
        ],
        "md5": "$md5_value",
        "sha256": "$sha256_value"
    }
]
EOF

    echo "JSON 文件已生成并写入 $version/agents/$os_type/config.json"
	
	echo "复制文件到新目录..."
    case $os_type in
        "windows")
            cp "$filename" "$output_dir/ASec_Client_Setup_Win32.exe"
            ;;
        "darwin")
            cp "$filename" "$output_dir/ASec_Client_Installer.pkg"
            ;;
        "android")
            cp "$filename" "$output_dir/ASec_Client.apk"
            ;;
        *)
            echo "未知的系统类型: $os_type"
            ;;
    esac
}
parse_args() {
    for arg in "$@"; do
        case $arg in
            --folder_name=*)
                folder_name="${arg#*=}"
                shift
                ;;
            --windows_version=*)
                windows_version="${arg#*=}"
                shift
                ;;
            --windows_file=*)
                windows_file="${arg#*=}"
                shift
                ;;
            --darwin_version=*)
                darwin_version="${arg#*=}"
                shift
                ;;
            --darwin_file=*)
                darwin_file="${arg#*=}"
                shift
                ;;
            --android_version=*)
                android_version="${arg#*=}"
                shift
                ;;
            --android_file=*)
                android_file="${arg#*=}"
                shift
                ;;
            --help)
                echo "Usage: $0 --folder_name=value --windows_version=value --windows_file=value --darwin_version=value --darwin_file=value --android_version=value --android_file=value"
                exit 0
                ;;
            *)
                echo "Unknown argument: $arg"
                exit 1
                ;;
        esac
    done
}

main() {

    parse_args "$@"
	
	mkdir -p "$folder_name"


	 # 清理子目录
    echo "清理 $folder_name 子目录"
    find "$folder_name" -mindepth 1 -type d -exec rm -rf {} + 2>/dev/null || true
	
	cd "$folder_name"
    
	if [[ -n "$windows_version" && -n "$windows_file" ]]; then
        generate_config "windows" "$windows_version" "$windows_file"
    fi

    # 调用函数处理 Darwin (macOS)，跳过空参数
    if [[ -n "$darwin_version" && -n "$darwin_file" ]]; then
        generate_config "darwin" "$darwin_version" "$darwin_file"
    fi

    # 调用函数处理 Android，跳过空参数
    if [[ -n "$android_version" && -n "$android_file" ]]; then
        generate_config "android" "$android_version" "$android_file"
    fi
	
	cd ..
	
	# 创建 tar.gz 压缩包
	echo "创建 tar.gz 压缩包: $folder_name.tar.gz"
	tar -czvf "$folder_name.tar.gz" "$folder_name"

	echo "创建 .pkg加密包"
	./cmd/pkg_enc.exe "$folder_name.tar.gz" "$folder_name.pkg" 
	echo "打包成功,客户端加密包:$folder_name.pkg"
}


# 示例调用: ./script.sh --folder_name=myfolder --windows_version="********-dev Build86" --windows_file="ASec_Client_Setup_******** Build86.exe" --darwin_version="********-dev Build86" --darwin_file="ASec_Client_Installer_********.pkg" --android_version="********-dev Build86" --android_file="ASec_Client.apk"
main "$@"
